<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('games_keys', function (Blueprint $table) {
            $table->string('kind')->default('playfiver')->nullable()->after('id');

            $table->renameColumn('playfiver_url', 'url');
            $table->renameColumn('playfiver_rtp', 'rtp');
            $table->renameColumn('playfiver_secret', 'secret');
            $table->renameColumn('playfiver_code', 'code');
            $table->renameColumn('playfiver_token', 'token');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('games_keys', function (Blueprint $table) {
            $table->dropColumn('kind');

            $table->renameColumn('url', 'playfiver_url');
            $table->renameColumn('rtp', 'playfiver_rtp');
            $table->renameColumn('secret', 'playfiver_secret');
            $table->renameColumn('code', 'playfiver_code');
            $table->renameColumn('token', 'playfiver_token');
        });
    }
};
