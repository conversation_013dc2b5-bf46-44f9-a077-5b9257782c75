<?php

use App\Enums\KindOfGatewayEnum;
use App\Models\Gateway;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $gateway = Gateway::first();
        $digitopay_uri = $gateway->digitopay_uri;
        $digitopay_cliente_id = $gateway->digitopay_cliente_id;
        $digitopay_cliente_secret = $gateway->digitopay_cliente_secret;
        $suitpay_uri = $gateway->suitpay_uri;
        Schema::table('gateways', function (Blueprint $table) {
            $table->string('kind');
            $table->string('url');
            $table->string('key')->nullable();
            $table->string('token');
            $table->string('secret');

            $table->dropColumn('suitpay_uri');
            $table->dropColumn('suitpay_cliente_id');
            $table->dropColumn('suitpay_cliente_secret');

            $table->dropColumn('stripe_production');
            $table->dropColumn('stripe_public_key');
            $table->dropColumn('stripe_secret_key');
            $table->dropColumn('stripe_webhook_key');

            $table->dropColumn('bspay_uri');
            $table->dropColumn('bspay_cliente_id');
            $table->dropColumn('bspay_cliente_secret');

            $table->dropColumn('public_key');
            $table->dropColumn('private_key');

            $table->dropColumn('mp_client_id');
            $table->dropColumn('mp_public_key');
            $table->dropColumn('mp_client_secret');
            $table->dropColumn('mp_access_token');

            $table->dropColumn('digitopay_uri');
            $table->dropColumn('digitopay_cliente_id');
            $table->dropColumn('digitopay_cliente_secret');
        });

        Gateway::first()->delete();

        $sigitopayDados = [
            'kind' => KindOfGatewayEnum::Digitopay->value,
            'url' => $digitopay_uri,
            'key' => '',
            'token' => $digitopay_cliente_id,
            'secret' => $digitopay_cliente_secret,
        ];
        $res = Gateway::create($sigitopayDados);


        $suitpayDados = [
            'kind' => KindOfGatewayEnum::Suitpay->value,
            'url' => $suitpay_uri,
            'key' => '',
            'token' => '',
            'secret' => '',
        ];
        $res = Gateway::create($suitpayDados);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (
            !Schema::hasColumn('gateways', 'phone')
        )
            $digitopay = Gateway::where(
                'kind',
                KindOfGatewayEnum::Digitopay->value,
            )->first();
        $suitpay = Gateway::where(
            'kind',
            KindOfGatewayEnum::Suitpay->value,
        )->first();
        $suitpay_uri = $suitpay->url;
        $digitopay_uri = $digitopay->url;
        $digitopay_cliente_id = $digitopay->token;
        $digitopay_cliente_secret = $digitopay->secret;
        Gateway::whereIn('kind', [KindOfGatewayEnum::Digitopay->value, KindOfGatewayEnum::Suitpay->value])->delete();
        Schema::table('gateways', function (Blueprint $table) {
            $table->dropColumn('kind');
            $table->dropColumn('url');
            $table->dropColumn('key');
            $table->dropColumn('token');
            $table->dropColumn('secret');

            $table->string('suitpay_uri');
            $table->string('suitpay_cliente_id');
            $table->string('suitpay_cliente_secret');

            $table->string('stripe_production');
            $table->string('stripe_public_key');
            $table->string('stripe_secret_key');
            $table->string('stripe_webhook_key');

            $table->string('bspay_uri');
            $table->string('bspay_cliente_id');
            $table->string('bspay_cliente_secret');

            $table->string('public_key');
            $table->string('private_key');

            $table->string('mp_client_id');
            $table->string('mp_client_secret');
            $table->string('mp_public_key');
            $table->string('mp_access_token');

            $table->string('digitopay_uri');
            $table->string('digitopay_cliente_id');
            $table->string('digitopay_cliente_secret');
        });
        Gateway::create([
            'suitpay_uri' => $suitpay_uri,
            'digitopay_uri' => $digitopay_uri,
            'digitopay_cliente_id' => $digitopay_cliente_id,
            'digitopay_cliente_secret' => $digitopay_cliente_secret,
        ]);
    }
};
