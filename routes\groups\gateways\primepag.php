<?php


use App\Http\Controllers\Gateway\PrimePagController;
use Illuminate\Support\Facades\Route;


Route::prefix('primepag')
    ->group(function ()
    {
        Route::post('callback', [PrimePagController::class, 'callbackMethod']);

        Route::middleware(['admin.filament'])
            ->group(function ()
            {
                Route::get('withdrawal/{id}/{action}', [PrimePagController::class, 'withdrawalFromModal'])->name('primepag.withdrawal');
                Route::get('withdrawal-manual/{id}/{action}', [PrimePagController::class, 'withdrawalFromModalManual'])->name('primepag.withdrawalManual');
                Route::get('cancelwithdrawal/{id}/{action}', [PrimePagController::class, 'cancelWithdrawalFromModal'])->name('primepag.cancelwithdrawal');
            });
    });

