<?php

namespace App\Http\Controllers\Gateway;

use App\Models\AffiliateWithdraw;
use App\Models\SuitPayPayment;
use App\Models\Wallet;
use App\Models\Withdrawal;
use App\Traits\Gateways\PrimePagTrait;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Filament\Notifications\Notification;

class PrimePagController extends Controller
{
    use PrimePagTrait;


    /**
     * @param Request $request
     * @return null
     */
    public function getQRCodePix(Request $request)
    {
        return self::requestQrcodePrimePag($request);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function consultStatusTransactionPix(Request $request)
    {
        return self::consultStatusTransactionPrimePag($request);
    }

    public function callbackMethod(Request $request)
    {
        return self::callbackMethodPrimePag($request);
    }

    /**
     * Display the specified resource.
     */
    public function withdrawalFromModal($id, $action)
    {
        if ($action == 'user') {
            return $this->confirmWithdrawalUser($id);
        }

        if ($action == 'affiliate') {
            return $this->confirmWithdrawalAffiliate($id);
        }
    }

    /**
     * Display the specified resource.
     */
    public function withdrawalFromModalManual($id, $action)
    {
        if ($action == 'user') {
            return $this->confirmWithdrawalUserManual($id);
        }

        if ($action == 'affiliate') {
            return $this->confirmWithdrawalAffiliateManual($id);
        }
    }

    /**
     * Cancel Withdrawal
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancelWithdrawalFromModal($id, $action)
    {
        if ($action == 'user') {
            return $this->cancelWithdrawalUser($id);
        }

        if ($action == 'affiliate') {
            return $this->cancelWithdrawalAffiliate($id);
        }
    }

    /**
     * @param $id
     * @return \Illuminate\Http\RedirectResponse|void
     */
    public function confirmWithdrawalUser($id)
    {
        $withdrawal = Withdrawal::find($id);

        if (!empty($withdrawal)) {
            $suitpayment = SuitPayPayment::create([
                'withdrawal_id' => $withdrawal->id,
                'user_id'       => $withdrawal->user_id,
                'pix_key'       => $withdrawal->pix_key,
                'pix_type'      => $withdrawal->pix_type,
                'amount'        => $withdrawal->amount,
                'observation'   => 'primegag',
            ]);

            if ($suitpayment) {
                $params = [
                    "initiation_type"   => "dict",
                    "idempotent_id"     => $withdrawal->id,
                    "receiver_name"     => $withdrawal->user->name,
                    "receiver_document" => $withdrawal->user->cpf,
                    "value_cents"       => preg_replace('/[^0-9]/', '', $withdrawal->amount . ''),
                    "pix_key_type"      => str_replace('phoneNumber', 'phone', $withdrawal->pix_type),
                    "pix_key"           => $withdrawal->pix_type == 'phoneNumber' ? '+55' . $withdrawal->pix_key : $withdrawal->pix_key,
                    "authorized"        => false
                ];

                $resp = self::pixCashOutPrimePag($params);

                if (isset($resp['message'])) {
                    $withdrawal->update(['status' => 1]);
                    Notification::make()
                        ->title('Saque solicitado')
                        ->body($resp['message'])
                        ->success()
                        ->send();

                    return back();
                } else {
                    Notification::make()
                        ->title('Erro no saque')
                        ->body($resp['error'])
                        ->danger()
                        ->send();

                    return back();
                }
            }
        }
        Notification::make()
            ->title('Erro no saque')
            ->body('Pedido de saque não encontrado!')
            ->danger()
            ->send();

        return back();
    }

    /**
     * @param $id
     * @return \Illuminate\Http\RedirectResponse|void
     */
    public function confirmWithdrawalUserManual($id)
    {
        $withdrawal = Withdrawal::find($id);

        if (!empty($withdrawal)) {
            $suitpayment = SuitPayPayment::create([
                'withdrawal_id' => $withdrawal->id,
                'user_id'       => $withdrawal->user_id,
                'pix_key'       => $withdrawal->pix_key,
                'pix_type'      => $withdrawal->pix_type,
                'amount'        => $withdrawal->amount,
                'observation'   => 'primegag-manual',
            ]);

            if ($suitpayment && $withdrawal->update(['status' => 1])) {
                Notification::make()
                    ->title('Saque solicitado')
                    ->body('Pagamento manual realizado com êxito!')
                    ->success()
                    ->send();

                return back();
            } else {
                Notification::make()
                    ->title('Saque solicitado')
                    ->body('Pagamento manual não realizado!')
                    ->success()
                    ->send();

                return back();
            }
        }
        Notification::make()
            ->title('Erro no saque')
            ->body('Pedido de saque não encontrado!')
            ->danger()
            ->send();

        return back();
    }

    /**
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    private function cancelWithdrawalUser($id)
    {
        $withdrawal = Withdrawal::find($id);
        if (!empty($withdrawal)) {
            $wallet = Wallet::where('user_id', $withdrawal->user_id)
                ->where('currency', $withdrawal->currency)
                ->first();

            if (!empty($wallet)) {
                $wallet->increment('balance_withdrawal', $withdrawal->amount);

                $withdrawal->update(['status' => 2]);
                Notification::make()
                    ->title('Saque cancelado')
                    ->body('Saque cancelado com sucesso')
                    ->success()
                    ->send();

                return back();
            }
            return back();
        }
        return back();
    }

    /**
     * @param $id
     * @return \Illuminate\Http\RedirectResponse|void
     */
    public function confirmWithdrawalAffiliate($id)
    {
        $withdrawal = AffiliateWithdraw::find($id);

        if (!empty($withdrawal)) {
            $suitpayment = SuitPayPayment::create([
                'withdrawal_id' => $withdrawal->id,
                'user_id'       => $withdrawal->user_id,
                'pix_key'       => $withdrawal->pix_key,
                'pix_type'      => $withdrawal->pix_type,
                'amount'        => $withdrawal->amount,
                'observation'   => 'primegag',
            ]);

            if ($suitpayment) {
                $parm = [
                    "initiation_type"   => "dict",
                    "idempotent_id"     => $withdrawal->id,
                    "receiver_name"     => $withdrawal->user->name,
                    "receiver_document" => $withdrawal->user->cpf,
                    "value_cents"       => preg_replace('/[^0-9]/', '', $withdrawal->amount . ''),
                    "pix_key_type"      => str_replace('phoneNumber', 'phone', $withdrawal->pix_type),
                    "pix_key"           => $withdrawal->pix_type == 'phoneNumber' ? '+55' . $withdrawal->pix_key : $withdrawal->pix_key,
                    "authorized"        => false
                ];

                $resp = self::pixCashOutPrimePag($parm);

                if ($resp) {
                    $withdrawal->update(['status' => 1]);
                    Notification::make()
                        ->title('Saque solicitado')
                        ->body('Saque solicitado com sucesso')
                        ->success()
                        ->send();

                    return back();
                } else {
                    Notification::make()
                        ->title('Erro no saque')
                        ->body('Erro ao solicitar o saque')
                        ->danger()
                        ->send();

                    return back();
                }
            }
        }
    }

    /**
     * @param $id
     * @return \Illuminate\Http\RedirectResponse|void
     */
    public function confirmWithdrawalAffiliateManual($id)
    {
        $withdrawal = AffiliateWithdraw::find($id);

        if (!empty($withdrawal)) {
            $suitpayment = SuitPayPayment::create([
                'withdrawal_id' => $withdrawal->id,
                'user_id'       => $withdrawal->user_id,
                'pix_key'       => $withdrawal->pix_key,
                'pix_type'      => $withdrawal->pix_type,
                'amount'        => $withdrawal->amount,
                'observation'   => 'primegag-manual',
            ]);

            if ($suitpayment && $withdrawal->update(['status' => 1])) {
                Notification::make()
                    ->title('Saque solicitado')
                    ->body('Pagamento manual realizado com êxito')
                    ->success()
                    ->send();

                return back();
            } else {
                Notification::make()
                    ->title('Erro no saque')
                    ->body('Erro ao solicitar o pagamento manual')
                    ->danger()
                    ->send();

                return back();
            }
        }
    }

    /**
     * @param $id
     * @return \Illuminate\Http\RedirectResponse
     */
    private function cancelWithdrawalAffiliate($id)
    {
        $withdrawal = AffiliateWithdraw::find($id);
        if (!empty($withdrawal)) {
            $wallet = Wallet::where('user_id', $withdrawal->user_id)
                ->where('currency', $withdrawal->currency)
                ->first();

            if (!empty($wallet)) {
                $wallet->increment('refer_rewards', $withdrawal->amount);

                $withdrawal->update(['status' => 2]);
                Notification::make()
                    ->title('Saque cancelado')
                    ->body('Saque cancelado com sucesso')
                    ->success()
                    ->send();

                return back();
            }
            return back();
        }
        return back();
    }
}
