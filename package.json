{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "clean": "vite clean"}, "devDependencies": {"autoprefixer": "^10.4.16", "axios": "^1.6.2", "laravel-vite-plugin": "^0.8.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^4.5.2"}, "dependencies": {"@vitejs/plugin-vue": "^4.4.0", "@vue-stripe/vue-stripe": "^4.5.0", "@vueuse/core": "^10.1.0", "flowbite": "^2.0.0", "flowbite-datepicker": "^1.2.2", "gsap": "^3.12.5", "howler": "^2.2.3", "jquery": "^3.7.0", "laravel-echo": "^1.15.3", "laravel-vue-i18n": "^2.7.1", "laravel-vue-pagination": "^4.1.3", "maska": "^2.1.10", "moment": "^2.29.4", "pinia": "^2.0.34", "pixi.js": "^8.1.5", "pusher-js": "^8.3.0", "qrcode-vue3": "^1.6.8", "vue": "^3.3.7", "vue-axios": "^3.5.2", "vue-fullscreen": "^3.1.1", "vue-lazyload": "^3.0.0", "vue-router": "^4.2.5", "vue-toastification": "^2.0.0-rc.5", "vue3-carousel": "^0.3.1"}}