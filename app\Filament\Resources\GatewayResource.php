<?php

namespace App\Filament\Resources;

use App\Enums\KindOfGatewayEnum;
use App\Filament\Resources\GatewayResource\Pages;
use App\Filament\Resources\GatewayResource\RelationManagers;
use App\Models\Gateway;
use Filament\Forms;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class GatewayResource extends Resource
{
    protected static ?string $model = Gateway::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Gateway')
                    // ->description('Acesse o painel da API.')
                    ->schema([
                        Group::make()
                            ->schema([
                                Select::make('kind')
                                    ->label('Tipo')
                                    ->options(KindOfGatewayEnum::class)
                                    ->unique(ignorable: fn($record) => $record)
                                    ->required(),
                                TextInput::make('url')
                                    ->label('URL')
                                    ->placeholder('Digite aqui a URL')
                                    ->maxLength(191)
                                    ->required()
                                    ->url()
                                    ->prefix('https://')
                                    ->suffixIcon('heroicon-m-globe-alt'),
                                TextInput::make('key')
                                    ->label('Código')
                                    ->placeholder('Digite aqui o código')
                                    ->maxLength(191),
                            ])->columns(3),
                        Group::make()
                            ->schema([
                                TextInput::make('token')
                                    ->label('Token. ClienteId ou PublicKey')
                                    ->password()
                                    ->required()
                                    ->revealable()
                                    ->placeholder('Digite aqui o token')
                                    ->maxLength(191),
                                TextInput::make('secret')
                                    ->label('Código secreto. ClientSecret ou SecretKey ou PrivateKey')
                                    ->password()
                                    ->required()
                                    ->revealable()
                                    ->placeholder('Digite aqui o código secreto')
                                    ->maxLength(191),
                            ])
                            ->columns(2),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('kind')
                    ->label('Tipo')
                    ->badge(),
                TextColumn::make('url')
                    ->label('URL')
                    ->sortable()
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGateways::route('/'),
            'create' => Pages\CreateGateway::route('/create'),
            'edit' => Pages\EditGateway::route('/{record}/edit'),
        ];
    }
}
