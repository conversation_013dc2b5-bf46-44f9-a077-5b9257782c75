<?php

namespace App\Traits\Gateways;

use App\Enums\KindOfGatewayEnum;
use App\Models\AffiliateHistory;
use App\Models\Deposit;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Notifications\NewDepositNotification;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use App\Helpers\Core as Helper;
use App\Models\Gateway;
use App\Models\SuitPayPayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\FacadesDB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

trait PrimePagTrait
{
    /**
     * @var $uri
     * @var $clientId
     * @var $clientSecret
     */
    protected static string $uri;
    protected static string $clientId;
    protected static string $clientSecret;
    protected static string $callbackUrl;
    protected static string $token;
    protected static string $authorizationValue;

    private static function generateCredentialsPrimePag()
    {
        $gateway = Gateway::where('kind', KindOfGatewayEnum::Primepag->value)->first();
        if (!empty($gateway)) {
            self::$uri = $gateway->url;
            self::$clientId = $gateway->token;
            self::$clientSecret = $gateway->secret;
            self::$token ??= self::getToken();
            self::$authorizationValue = md5($gateway->token . $gateway->secret);
        }
    }

    /**
     * Request QRCODE
     * Metodo para solicitar uma QRCODE PIX
     *
     * @return array
     */
    public static function requestQrcodePrimePag($request)
    {
        self::generateCredentialsPrimePag();
        self::removeAndCreateWebHookQrCodes();
        $setting = Helper::getSetting();
        $rules = [
            'amount' => ['required', 'numeric', "between:$setting->min_deposit,$setting->max_deposit"],
            'cpf'    => ['required', 'max:255'],
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        DB::table('debug')->insert(['text' => json_encode($request->all())]);

        $stringGenerate = Str::uuid();
        $headers = ['Authorization' => 'Bearer ' . self::$token];
        $endpoint = self::$uri . '/v1/pix/qrcodes';
        $data = [
            "value_cents" => number_format((float) $request->amount, 2, '', ''),
            "generator_name" => auth('api')->user()->name,
            "generator_document" => Helper::soNumero($request->cpf),
            "expiration_time" => "1800",
            "external_reference" => $stringGenerate
        ];

        $response = Http::withHeaders($headers)->post($endpoint, $data);
        $responseData = $response->json();

        if (env('APP_DEBUG'))
            Log::info(array_merge(['PrimePag' => 'requestQrcode'], $responseData));

        if ($response->successful()) {
            $qrcode = $responseData['qrcode'];

            self::generateTransactionPrimePag($qrcode['reference_code'], Helper::amountPrepare($request->amount), $request->accept_bonus); /// gerando historico
            self::generateDepositPrimePag($qrcode['reference_code'], Helper::amountPrepare($request->amount), $request->deposit_value_id); /// gerando deposito

            return [
                'status' => true,
                'idTransaction' => $qrcode['reference_code'],
                'qrcode' => $qrcode['content']
            ];
        }

        return [
            'status' => false,
            'errors' => $responseData
        ];
    }

    /**
     * Consult Status Transaction
     * Consultar o status da transação
     *
     *
     * @param $request
     * @return \Illuminate\Http\JsonResponse
     */
    public static function consultStatusTransactionPrimePag($request)
    {
        self::generateCredentialsPrimePag();
        $headers = ['Authorization' => 'Bearer ' . self::$token];
        $baseUrl = self::$uri . '/v1/pix/qrcodes/' . $request->idTransaction;
        $response = Http::withHeaders($headers)->get($baseUrl);
        $responseData = $response->json();

        if (env('APP_DEBUG'))
            Log::info(array_merge(['PrimePag' => 'consultStatusTransactionPrimePag'], $responseData));

        if ($response->successful()) {
            $qrcode = $responseData['qrcode'];
            if ($qrcode['status'] == 'paid') {
                DB::table('debug')->insert(['text' => json_encode($request->all())]);
                if (self::finalizePaymentPrimePag($request->idTransaction)) {
                    return response()->json(['status' => 'PAID'], 200);
                }
            }
        }

        return response()->json(['status' => $qrcode['status']], 400);
    }

    /**
     * @param $idTransaction
     *
     * @return bool
     */
    public static function finalizePaymentPrimePag($idTransaction): bool
    {

        $transaction = Transaction::where('payment_id', $idTransaction)->first();
        $setting = Helper::getSetting();
        if (!empty($transaction)) {
            if ($transaction->status) {
                return true;
            }
            $user = User::find($transaction->user_id);

            $wallet = Wallet::where('user_id', $transaction->user_id)->first();
            if (!empty($wallet)) {

                /// verifica se é o primeiro deposito, verifica as transações, somente se for transações concluidas
                $checkTransactions = Transaction::where('user_id', $transaction->user_id)
                    ->where('status', 1)
                    ->count();

                if ($checkTransactions == 0 || empty($checkTransactions)) {
                    if ($transaction->accept_bonus) {
                        /// pagar o bonus
                        $bonus = Helper::porcentagem_xn($setting->initial_bonus, $transaction->price);
                        $wallet->increment('balance_bonus', $bonus);

                        if (!$setting->disable_rollover) {
                            $wallet->update(['balance_bonus_rollover' => $bonus * $setting->rollover]);
                        }
                    }
                }

                /// rollover deposito
                if ($setting->disable_rollover == false) {
                    $wallet->increment('balance_deposit_rollover', ($transaction->price * intval($setting->rollover_deposit)));
                }

                /// acumular bonus
                Helper::payBonusVip($wallet, $transaction->price);

                /// quando tiver desativado o rollover, ele manda o dinheiro depositado direto pra carteira de saque
                if ($setting->disable_rollover) {
                    $wallet->increment('balance_withdrawal', $transaction->price); /// carteira de saque
                } else {
                    $wallet->increment('balance', $transaction->price); /// carteira de jogos, não permite sacar
                }

                if ($transaction->update(['status' => 1])) {
                    $deposit = Deposit::where('payment_id', $idTransaction)->where('status', 0)->first();
                    if (!empty($deposit)) {
                        $wallet->increment('free_spins', $deposit->depositValue->spins);

                        /// fazer o deposito em cpa
                        $affHistoryCPA = AffiliateHistory::where('user_id', $user->id)
                            ->where('commission_type', 'cpa')
                            //->where('deposited', 1)
                            //->where('status', 0)
                            ->first();

                        if (env('APP_DEBUG'))
                            Log::info(json_encode($affHistoryCPA));

                        if (!empty($affHistoryCPA)) {
                            /// faz uma soma de depositos feitos pelo indicado
                            $affHistoryCPA->increment('deposited_amount', $transaction->price);

                            /// verifcia se já pode receber o cpa
                            $sponsorCpa = User::find($user->inviter);

                            if (env('APP_DEBUG'))
                                Log::info(json_encode($sponsorCpa));
                            /// verifica se foi pago ou nnão
                            if (!empty($sponsorCpa) && $affHistoryCPA->status == 'pendente') {
                                Log::info('Deposited Amount: ' . $affHistoryCPA->deposited_amount);
                                Log::info('Affiliate Baseline: ' . $sponsorCpa->affiliate_baseline);
                                Log::info('Amount: ' . $deposit->amount);

                                if ($affHistoryCPA->deposited_amount >= $sponsorCpa->affiliate_baseline || $deposit->amount >= $sponsorCpa->affiliate_baseline) {
                                    $walletCpa = Wallet::where('user_id', $affHistoryCPA->inviter)->first();
                                    if (env('APP_DEBUG'))
                                        Log::info(json_encode($walletCpa));
                                    if (!empty($walletCpa)) {
                                        if (env('APP_DEBUG'))
                                            Log::info('Affiliate CPA: ' . $sponsorCpa->affiliate_cpa);

                                        /// paga o valor de CPA
                                        $walletCpa->increment('refer_rewards', $sponsorCpa->affiliate_cpa); /// coloca a comissão
                                        $affHistoryCPA->update(['status' => 1, 'commission_paid' => $sponsorCpa->affiliate_cpa]); /// desativa cpa
                                    }
                                }
                            }
                        }

                        if ($deposit->update(['status' => 1])) {
                            $admins = User::where('role_id', 0)->get();
                            foreach ($admins as $admin) {
                                $admin->notify(new NewDepositNotification($user->name, $transaction->price));
                            }

                            return true;
                        }
                        return false;
                    }
                    return false;
                }

                return false;
            }
            return false;
        }
        return false;
    }

    /**
     * @param $idTransaction
     * @param $amount
     *
     * @return void
     */
    private static function generateDepositPrimePag($idTransaction, $amount, $depositValueId)
    {
        $userId = auth('api')->user()->id;
        $wallet = Wallet::where('user_id', $userId)->first();

        $data = [
            'deposit_value_id' => $depositValueId,
            'payment_id' => $idTransaction,
            'user_id'   => $userId,
            'amount'    => $amount,
            'type'      => 'pix',
            'currency'  => $wallet->currency,
            'symbol'    => $wallet->symbol,
            'status'    => 0
        ];
        Deposit::create($data);
    }

    /**
     * @param $idTransaction
     * @param $amount
     *
     * @return void
     */
    private static function generateTransactionPrimePag($idTransaction, $amount, $accept_bonus)
    {
        $setting = Helper::getSetting();

        $data = [
            'payment_id'     => $idTransaction,
            'user_id'        => auth('api')->user()->id,
            'payment_method' => 'pix',
            'price'          => $amount,
            'currency'       => $setting->currency_code,
            'accept_bonus'   => $accept_bonus,
            'status'         => 0,
            'reference'      => 'primepag'
        ];
        Transaction::create($data);
    }

    private static function getToken()
    {
        $body = ["grant_type" => 'client_credentials'];
        $headers = ['Content-Type' => 'application/json', 'Authorization' => self::encodeCredetials()];
        $endpoint = self::$uri . '/auth/generate_token';

        $response = Http::withHeaders($headers)
            ->post($endpoint, $body);
        $responseData = $response->json();

        if ($response->successful())
            return $responseData['access_token'];
        else
            return $responseData['error'];
    }

    private static function encodeCredetials()
    {
        return base64_encode(self::$clientId . ':' . self::$clientSecret);
    }

    private static function removeAndCreateWebHookQrCodes()
    {
        self::removeWebHookQrCodes();
        if (!self::findWebHookQrCodes())
            self::createWebHookQrCodes();
    }

    private static function findWebHookQrCodes()
    {
        $headers = ['Authorization' => 'Bearer ' . self::$token];
        $endpoint = self::$uri . '/v1/webhooks';
        $response = Http::withHeaders($headers)->get($endpoint);
        $responseData = $response->json();

        if (env('APP_DEBUG'))
            Log::info(array_merge(['PrimePag' => 'findWebHookQrCodes'], $responseData));

        if ($response->successful()) {
            $webhooks = $responseData['webhooks'];
            if (!empty($webhooks)) {
                $specific_value = 1;
                $filtered_array = array_filter($webhooks, function ($obj) use ($specific_value) {
                    return $obj['webhook_type_id'] == $specific_value;
                });
                return !empty($filtered_array);
            }
        }
        return false;
    }

    private static function createWebHookQrCodes()
    {
        $headers = ['Authorization' => 'Bearer ' . self::$token];
        $endpoint = self::$uri . '/v1/webhooks/1'; # pix_qrcodes
        $data = [
            'url' => env('APP_URL', 'http://localhost') . '/primepag/callback',
            "authorization" => self::$authorizationValue
        ];

        $response = Http::withHeaders($headers)->post($endpoint, $data);
        $responseData = $response->json();

        if (env('APP_DEBUG'))
            Log::info(array_merge(['PrimePag' => 'createWebHookQrCodes'], $responseData));

        if ($response->successful()) {
            return !empty($responseData['msg']);
        }
        return false;
    }

    private static function removeWebHookQrCodes()
    {
        $headers = ['Authorization' => 'Bearer ' . self::$token];
        $endpoint = self::$uri . '/v1/webhooks/1';
        $response = Http::withHeaders($headers)->delete($endpoint);
        $responseData = $response->json();

        if (env('APP_DEBUG'))
            Log::info(array_merge(['PrimePag' => 'removeWebHookQrCodes'], $responseData));

        if ($response->successful()) {
            return !empty($responseData['msg']);
        }
        return false;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function callbackMethodPrimePag(Request $request)
    {
        $data = $request->all();

        DB::table('debug')->insert(['text' => json_encode($data)]);

        $message = $data['message'];

        if (isset($data['notification_type']) && $data['notification_type'] == 'pix_qrcode') {
            if ($message['status'] == "paid") {
                if (self::finalizePaymentPrimePag($message['reference_code'])) {
                    return response()->json(['status' => 'PAID']);
                }
            }
        }
        return response()->json(['status' => $message['status']], 200);
    }

    /**
     * @param $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public static function pixCashOutPrimePag(array $params)
    {
        self::generateCredentialsPrimePag();

        $headers = ['Authorization' => 'Bearer ' . self::$token];
        $endpoint = self::$uri . '/v1/pix/payments'; # pix_qrcodes

        $response = Http::withHeaders($headers)->post($endpoint, $params);

        $responseData = $response->json();
        if ($responseData && env('APP_DEBUG'))
            Log::info(array_merge(['PrimePag' => 'pixCashOutPrimePag'], $responseData));

        if ($response->successful()) {
            $payment = $responseData['payment'];
            if ($payment && $payment['status'] == 'completed') {
                $primePagPayment = SuitPayPayment::lockForUpdate()->where(['withdrawal_id' => $payment['idempotent_id']])->first();
                if (empty($primePagPayment))
                    return ['error' => 'Pedido de pagamento não eontrado'];

                $primePagPayment->update(['status' => 1, 'payment_id' => $payment['reference_code']]);
                return ['message' => 'Saque solicitado com sucesso.'];
            } else {
                if ($payment['status'] == 'sent') {
                    return ['error' => 'Pagamento aguardando confirmação.'];
                } else if ($payment['status'] == 'canceled') {
                    return ['error' => 'Pagamento Cancelado.'];
                } else if ($payment['status'] == 'auto_authorization') {
                    return ['error' => 'Pagamento enviado para autorização automática.'];
                } else if ($payment['status'] == 'authorization_pending') {
                    return ['error' => 'Autorização pendente.'];
                } else {
                    return ['error' => 'Erro desconhecido.'];
                }
            }
        } else {
            return ['error' => 'Não foi possível enviar o pagamento.'];
        }
    }
}
