stage_deploy:
  image: tetraweb/php
  artifacts:
    expire_in: 1 week
    paths:
      - build/
  rules:
    - if: $CI_COMMIT_BRANCH == "onlybetbr.com"
  before_script:
    - sudo apt-get update -y
    - sudo apt-get install zip unzip -qy
    - sudo apt-get purge --auto-remove nodejs -qy
    - curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
    - sudo apt-get install nodejs -y
    - curl -sS https://getcomposer.org/installer | php
    - php composer.phar install --ignore-platform-reqs --no-scripts --no-dev --no-interaction --prefer-dist
    - npm install && npm run build
    - git archive --format=zip -o project.zip HEAD # only new project
  script:
    - scp -i ~/.ssh/id_rsa_onlybetbr.com -P "$SSH_PORT" project.zip "$SSH_USER_ONLYBETBR"@"$SSH_HOST":/home/<USER>/htdocs/onlybetbr.com # only new project
    - ssh -i ~/.ssh/id_rsa_onlybetbr.com -p "$SSH_PORT" "$SSH_USER_ONLYBETBR"@"$SSH_HOST" 'cd /home/<USER>/htdocs/onlybetbr.com; unzip -o project.zip && rm project.zip'
