{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "ext-curl": "*", "ext-intl": "*", "ext-libxml": "*", "ext-simplexml": "*", "ext-zip": "*", "althinect/filament-spatie-roles-permissions": "^2.2", "aymanalhattami/filament-page-with-sidebar": "*", "creagia/filament-code-field": "^2.0", "filament/filament": "^3.1", "filament/widgets": "^3.1", "guzzlehttp/guzzle": "^7.2", "jackiedo/dotenv-editor": "^2.1", "laravel/framework": "^10.10", "laravel/sanctum": "^3.2", "laravel/socialite": "^5.10", "laravel/tinker": "^2.8", "laravellegends/pt-br-validator": "^10.0", "nwidart/laravel-modules": "^10.0", "pusher/pusher-php-server": "^7.2", "ramsey/uuid": "^4.7", "spatie/laravel-permission": "^6.1", "staudenmeir/eloquent-eager-limit": "^1.8", "stripe/stripe-php": "^13.6", "tymon/jwt-auth": "^2.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "Mo<PERSON>les/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}