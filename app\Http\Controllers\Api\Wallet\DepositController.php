<?php

namespace App\Http\Controllers\Api\Wallet;

use App\Enums\KindOfGatewayEnum;
use App\Http\Controllers\Controller;
use App\Models\Deposit;
use App\Traits\Gateways\PrimePagTrait;
use App\Traits\Gateways\SuitpayTrait;
use Illuminate\Http\Request;

class DepositController extends Controller
{
    use SuitpayTrait, PrimePagTrait;

    /**
     * @param Request $request
     * @return array|false[]
     */
    public function submitPayment(Request $request)
    {
        if ($request->gateway == KindOfGatewayEnum::Primepag->value)
            return self::requestQrcodePrimePag($request);
        else if ($request->gateway == 'suitpay')
            return self::requestQrcode($request);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function consultStatusTransactionPix(Request $request)
    {
        return self::consultStatusTransactionPix($request);
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $deposits = Deposit::whereUserId(auth('api')->id())->paginate();
        return response()->json(['deposits' => $deposits], 200);
    }
}
