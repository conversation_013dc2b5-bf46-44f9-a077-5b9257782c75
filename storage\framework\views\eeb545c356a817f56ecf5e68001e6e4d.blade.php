<?php extract(collect($attributes->getAttributes())->mapWithKeys(function ($value, $key) { return [Illuminate\Support\Str::camel(str_replace([':', '.'], ' ', $key)) => $value]; })->all(), EXTR_SKIP); ?>
@props(['field','inlineLabelVerticalAlignment'])
<x-filament-forms::field-wrapper :field="$field" :inline-label-vertical-alignment="$inlineLabelVerticalAlignment" >

{{ $slot ?? "" }}
</x-filament-forms::field-wrapper>