<style>
.teste-margin {
    background-color: var(--footer-color-dark);
    border-radius: 5px;
}
#imagem-cash {
    max-width: 300px;
    margin-top: -60px;
}
@media (min-width:1025px) and (max-width:1410px) {
    .padding-wallet {
        padding: 0px 4%;
    }
}
@media (min-width:640px) and (max-width:1024px) {
    .teste-margin {
        margin-top: 40px;
    }
}
@media (max-width:600px) {
    #imagem-cash {
        max-width: 150px;
        margin-top: 0px;
    }
}
@media (max-width:768px) {
    .padding-wallet {
      padding: 0px 4%;
    }
}
</style>
<template>
    <BaseLayout>
        <div v-if="setting != null" class="grid grid-cols-1 mx-auto w-full sm:max-w-[690px] lg:max-w-[1110px] w-full padding-wallet" style="border-radius: 5px;">
            <div class="">
                <div class="">

                </div>
                <div class="relative sm:max-w-[690px] lg:max-w-[1110px] mx-auto w-full" style="margin: 0 auto;border-radius: 5px;">
                    <div v-if="!isLoadingWallet" class="grid grid-cols-1 mt-3 " style="padding-top: 15px;border-radius: 5px;">
                        <div class="grid grid-cols-1 p-4 sm:max-w-[350px] lg:max-w-[550px] teste-margin">
                            <div class="flex ">
                                <div class="text-center flex justify-center items-center">

                                </div>
                                <div class="leading-4 ml-3 mr-3 flex justify-between gap-4 w-full">

                                    <h1>BRL</h1>

                                    <i style="color: #5B5E5F;font-size: 45px;opacity: .5;" class="fa-duotone fa-wallet"></i>
                                </div>

                            </div>
                            <div class="leading-4 ml-3 flex flex-col gap-5">
                                    <strong style="font-size: 2.5em; font-weight: bold">{{ state.currencyFormat(wallet?.total_balance ?? 0, wallet?.currency) }}</strong>
                                    R$ {{ state.currencyFormat((wallet?.balance_bonus ?? 0), wallet?.currency) }}
                                    <div class="mt-2 pb-3" style="display: flex;">
                                    <MakeDeposit :showMobile="false" :title="$t('Depositar')" />

                                                <RouterLink style="border-radius: 3px;color: var(--title-color);font-weight: bold;color: var(--title-color);background-color: #5B5E5F" :to="{ name: 'profileWithdraw' }" active-class="profile-menu-active" class="block px-8 text-sm text-gray-700 dark:text-gray-300 ml-3 ui-button-blue">
                                                    <span class="">

                                                    </span>
                                                    {{ $t('Sacar') }}
                                                </RouterLink>

                                </div>
                                </div>
                            <div class="flex">
                                <div class="text-center flex justify-center items-center">

                                </div>

                            </div>

                                <div class="w-1/2">
                                    <div class="">
                                        <div class="" :style="{ width: rolloverPercentage(parseFloat(wallet?.balance_deposit_rollover ?? 0))   }">

                                        </div>
                                        <div v-if="setting.disable_rollover === false || setting.rollover > 0" class="flex justify-between col-span-2">
                               <div class="flex w-1/2">
                                   <div class="text-center flex justify-center items-center">

                                   </div>

                               </div>
                               </div>

                       </div>
                       </div>
                       </div>
                        </div>




                        <div class=" w-full">
            <div class="grid grid-cols-1">
                <div class="col-span-1 hidden md:block w-full" >

                </div>
                <div class="col-span-2 relative w-full ">
                    <div style="background-color: var(--footer-color-dark);margin-top: 15px;" v-if="isLoading === false && wallet" class="flex flex-col gap-5 w-full bg-gray-200 hover:bg-gray-300/20 dark:bg-gray-700 p-4 mr-3 rounded">
                        <div class="header flex w-full">

                            <div class="flex flex-col">
                                <h3 style="color: var(--ci-primary-color);font-weight: bold;font-size:1.5em;">Minhas Transações</h3>
                                <p style="margin-bottom: -5px;opacity: .5" class="text-gray-400 text-sm mt-3">{{ $t('Saques:') }}</p>
                            </div>
                        </div>

                        <div v-if="withdraws && wallet" class="">
                            <div class="relative overflow-x-auto">
                                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-100 dark:bg-gray-700 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-6 py-3">
                                                {{ $t('Date') }}
                                            </th>
                                            <th scope="col" class="px-6 py-3">
                                                {{ $t('Value') }}
                                            </th>
                                            <th scope="col" class="px-6 py-3">
                                                {{ $t('Status') }}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(withdraw, index) in withdraws.data" :key="index" class="bg-white dark:bg-gray-800">
                                            <td class="px-6 py-4">
                                                {{ withdraw.dateHumanReadable }}
                                            </td>
                                            <td class="px-6 py-4">
                                                {{ withdraw?.amount }}
                                            </td>
                                            <td class="px-6 py-4">
                                                <span title="Pago" v-if="withdraw?.status === 1" class="text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:text-green-300"><i class="fa-sharp fa-solid fa-badge-check"></i></span>
                                                <span title="Processando..." v-if="withdraw?.status === 0" class=" text-yellow-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:text-yellow-300"><i class="fa-solid fa-hourglass-clock"></i></span>
                                            </td>

                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="header flex w-full">

                            <div class="flex flex-col">
                                <p style="margin-bottom: -5px;opacity: .5" class="text-gray-400 text-sm">{{ $t('Depósitos:') }}</p>
                            </div>
                        </div>

                        <div v-if="deposits && wallet" class="">
                            <div class="relative overflow-x-auto">
                                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-100 dark:bg-gray-700 dark:text-gray-400 ">
                                        <tr>
                                            <th scope="col" class="px-6 py-3">
                                                {{ $t('Date') }}
                                            </th>
                                            <th scope="col" class="px-6 py-3">
                                                {{ $t('Value') }}
                                            </th>
                                            <th scope="col" class="px-6 py-3">
                                                {{ $t('Status') }}
                                            </th>

                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(deposit, index) in deposits.data" :key="index" class="bg-white dark:bg-gray-800">
                                            <td class="px-6 py-4">
                                                {{ deposit.dateHumanReadable }}
                                            </td>
                                            <td class="px-6 py-4">
                                                {{ state.currencyFormat(parseFloat(deposit.amount), deposit.currency) }}
                                            </td>
                                            <td class="px-6 py-4">
                                                <span title="Pago" v-if="deposit.status === 1" class="text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:text-green-300"><i class="fa-sharp fa-solid fa-badge-check"></i></span>
                                                <span title="Processando..." v-if="deposit.status === 0" class=" text-yellow-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded dark:text-yellow-300"><i class="fa-solid fa-hourglass-clock"></i> </span>
                                            </td>

                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div v-if="isLoading" role="status" class="absolute -translate-x-1/2 -translate-y-1/2 top-2/4 left-1/2">
                        <i class="fa-duotone fa-spinner-third fa-spin" style="font-size: 45px;--fa-primary-color: var(--ci-primary-color); --fa-secondary-color: #000000;"></i>
                        <span class="sr-only">{{ $t('Loading') }}...</span>
                    </div>
                </div>
            </div>
        </div>
        </div>
        </div>
        </div>

    </BaseLayout>
</template>


<script>

import CustomPagination from "@/Components/UI/CustomPagination.vue";
import MakeDeposit from "@/Components/UI/MakeDeposit.vue";
import BaseLayout from "@/Layouts/BaseLayout.vue";
import WalletSideMenu from "@/Pages/Profile/Components/WalletSideMenu.vue";
import HttpApi from "@/Services/HttpApi.js";
import { useAuthStore } from "@/Stores/Auth.js";
import { useSettingStore } from "@/Stores/SettingStore.js";
import { RouterLink } from "vue-router";
import { useToast } from "vue-toastification";

export default {
    props: [],
    components: {MakeDeposit, CustomPagination, WalletSideMenu, BaseLayout, RouterLink },
    data() {
        return {
            isLoading: false,
            isLoadingWallet: true,
            wallet: {},
            mywallets: null,
            setting: null,
            withdraws: null,
            deposits: null,
        }
    },
    setup(props) {


        return {};
    },
    computed: {
        userData() {
            const authStore = useAuthStore();
            return authStore.user;
        },
    },
    mounted() {
        window.scrollTo(0, 0);
    },
    methods: {
        getWallet: function() {
            const _this = this;
            const _toast = useToast();

            HttpApi.get('profile/wallet')
                .then(response => {
                    _this.wallet = response.data.wallet;
                })
                .catch(error => {
                    Object.entries(JSON.parse(error.request.responseText)).forEach(([key, value]) => {
                        _toast.error(`${value}`);
                    });
                });
        },
        getWithdraws: function() {
            const _this = this;
            _this.isLoading = true;

            HttpApi.get('wallet/withdraw')
                .then(response => {
                    _this.withdraws = response.data.withdraws;
                    _this.isLoading = false;
                })
                .catch(error => {
                    Object.entries(JSON.parse(error.request.responseText)).forEach(([key, value]) => {
                        console.log(`${value}`);
                    });
                    _this.isLoading = false;
                });
        },
        getDeposits: function() {
            const _this = this;
            _this.isLoading = true;

            HttpApi.get('wallet/deposit')
                .then(response => {
                    _this.deposits = response.data.deposits;
                    _this.isLoading = false;
                })
                .catch(error => {
                    Object.entries(JSON.parse(error.request.responseText)).forEach(([key, value]) => {
                        console.log(`${value}`);
                    });
                    _this.isLoading = false;
                });
        },
        setWallet: function(id) {
            const _this = this;
            const _toast = useToast();
            _this.isLoadingWallet = true;

            HttpApi.post('profile/mywallet/'+ id, {})
                .then(response => {
                   _this.getMyWallet();
                    _this.isLoadingWallet = false;
                    window.location.reload();

                })
                .catch(error => {
                    Object.entries(JSON.parse(error.request.responseText)).forEach(([key, value]) => {
                        _toast.error(`${value}`);
                    });
                    _this.isLoadingWallet = false;
                });
        },
        getWallet: function() {
            const _this = this;
            const _toast = useToast();
            _this.isLoadingWallet = true;

            HttpApi.get('profile/wallet')
                .then(response => {
                    _this.wallet = response.data.wallet;
                    _this.isLoadingWallet = false;
                })
                .catch(error => {
                    Object.entries(JSON.parse(error.request.responseText)).forEach(([key, value]) => {
                        _toast.error(`${value}`);
                    });
                    _this.isLoadingWallet = false;
                });
        },
        getMyWallet: function() {
            const _this = this;
            const _toast = useToast();

            HttpApi.get('profile/mywallet')
                .then(response => {
                    _this.mywallets = response.data.wallets;
                })
                .catch(error => {
                    Object.entries(JSON.parse(error.request.responseText)).forEach(([key, value]) => {
                        _toast.error(`${value}`);
                    });
                });
        },
        getSetting: function() {
            const _this = this;
            const settingStore = useSettingStore();
            const settingData = settingStore.setting;

            if(settingData) {
                _this.setting = settingData;
            }

            _this.isLoading = false;
        },
        rolloverPercentage(balance) {
            return Math.max(0, ((balance / 100) * 100).toFixed(2));
        },
    },
    created() {
        this.getWallet();
        this.getMyWallet();
        this.getSetting();
        this.getWithdraws();
        this.getDeposits();
    },
    watch: {

    },
};
</script>

<style scoped>

</style>
