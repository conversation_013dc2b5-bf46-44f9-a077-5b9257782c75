<?php

namespace App\Filament\Resources;

use App\Enums\KindOfGameKeyEnum;
use App\Filament\Resources\GamesKeyResource\Pages;
use App\Filament\Resources\GamesKeyResource\RelationManagers;
use App\Models\GamesKey;
use Filament\Forms;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class GamesKeyResource extends Resource
{
    protected static ?string $model = GamesKey::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $title = 'Chaves dos Jogos';

    public static function canAccess(): bool
    {
        /** @var \App\Models\User */
        $user = Auth()->user();

        return $user->hasRole('admin');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Chave')
                    ->description('Acesse o painel da API.')
                    ->schema([
                        Group::make()
                            ->schema([
                                Select::make('kind')
                                    ->label('Tipo')
                                    ->options(KindOfGameKeyEnum::class)
                                    ->unique(ignorable: fn($record) => $record)
                                    ->required(),
                                TextInput::make('url')
                                    ->label('URL')
                                    ->placeholder('Digite aqui a URL')
                                    ->maxLength(191)
                                    ->required()
                                    ->url()
                                    ->prefix('https://')
                                    ->suffixIcon('heroicon-m-globe-alt'),
                                TextInput::make('code')
                                    ->label('Código do agente')
                                    ->placeholder('Digite aqui o código do agente')
                                    ->maxLength(191),
                            ])->columns(3),
                        Forms\Components\Group::make()
                            ->schema([
                                TextInput::make('secret')
                                    ->label('Código secreto do agente')
                                    ->password()
                                    ->required()
                                    ->revealable()
                                    ->placeholder('Digite aqui o código secreto do agente')
                                    ->maxLength(191),
                                TextInput::make('token')
                                    ->label('Token do agente')
                                    ->password()
                                    ->required()
                                    ->revealable()
                                    ->placeholder('Digite aqui o token do agente')
                                    ->maxLength(191),
                            ])
                            ->columns(2),
                        Forms\Components\Group::make()
                            ->schema([
                                TextInput::make('rtp')
                                    ->label('RTP dos usuário 10 a 95')
                                    ->placeholder('Digite aqui RTP dos usuário de 10 a 99 Padrão 50')
                                    ->numeric()
                                    ->maxLength(2)
                                    ->minValue(10)
                                    ->maxValue(97)
                                    ->length(2)
                                    ->default(70),
                            ])
                            ->columns(4),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('kind')
                    ->label('Tipo')
                    ->badge(),
                TextColumn::make('url')
                    ->label('URL')
                    ->sortable()
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGamesKeys::route('/'),
            'create' => Pages\CreateGamesKey::route('/create'),
            'edit' => Pages\EditGamesKey::route('/{record}/edit'),
        ];
    }
}
