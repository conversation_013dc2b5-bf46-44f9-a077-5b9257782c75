<template>
    
</template>

<script>
export default {
    props: ['isLoading'],
    components: {},
    data() {
        return {

        }
    },
    setup(props) {


        return {};
    },
    computed: {

    },
    mounted() {

    },
    methods: {

    },
    watch: {

    },
};
</script>

<style>
    .is-loading-component{
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100vw;
        height: 100vh;
    }
</style>
